"use client";

import React, { useState, useEffect } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import { StarterKit } from '@tiptap/starter-kit';
import TrackChangeExtension from './index';
import { HistoryPanel } from './HistoryPanel';
import { addSaveShortcut } from './utils';
import './styles.css';

export const ChangeTrackingExample: React.FC = () => {
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);

  const editor = useEditor({
    extensions: [
      StarterKit,
      TrackChangeExtension.configure({
        enabled: true,
        userId: 'user-123',
        userName: '<PERSON>',
      }),
    ],
    content: `
      <h1>Document Change Tracking Demo</h1>
      <p>This is a simple document with change tracking enabled.</p>
      <p>Try editing this text and then save your changes using Ctrl+S or the History panel.</p>
      <ul>
        <li>Make some edits to see how changes are tracked</li>
        <li>Use Ctrl+S to save changes</li>
        <li>Open the History panel to see your saved changes</li>
      </ul>
    `,
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none',
      },
    },
  });

  // Add keyboard shortcut for saving
  useEffect(() => {
    if (!editor) return;
    
    const cleanup = addSaveShortcut(editor);
    return cleanup;
  }, [editor]);

  const handleSaveClick = () => {
    if (!editor) return;
    editor.commands.saveDocument(`Manual save at ${new Date().toLocaleTimeString()}`);
  };

  const hasUnsavedChanges = editor?.commands.hasUncommittedChanges() || false;

  return (
    <div className="relative min-h-screen bg-gray-50">
      {/* Toolbar */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between max-w-4xl mx-auto">
          <h1 className="text-xl font-semibold text-gray-900">
            Change Tracking Demo
          </h1>
          
          <div className="flex items-center space-x-3">
            {/* Save Status */}
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${hasUnsavedChanges ? 'bg-yellow-500' : 'bg-green-500'}`} />
              <span className="text-sm text-gray-600">
                {hasUnsavedChanges ? 'Unsaved changes' : 'Saved'}
              </span>
            </div>

            {/* Save Button */}
            <button
              onClick={handleSaveClick}
              disabled={!hasUnsavedChanges}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                hasUnsavedChanges
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'bg-gray-200 text-gray-400 cursor-not-allowed'
              }`}
            >
              Save (Ctrl+S)
            </button>

            {/* History Button */}
            <button
              onClick={() => setIsHistoryOpen(!isHistoryOpen)}
              className="px-3 py-1 text-sm bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              History
            </button>
          </div>
        </div>
      </div>

      {/* Editor */}
      <div className="max-w-4xl mx-auto p-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <EditorContent editor={editor} />
        </div>
        
        {/* Instructions */}
        <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <h3 className="text-sm font-medium text-blue-900 mb-2">How to use:</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Edit the document above to see change tracking in action</li>
            <li>• Press <kbd className="px-1 py-0.5 bg-blue-200 rounded text-xs">Ctrl+S</kbd> to save changes</li>
            <li>• Click "History" to see your saved changes</li>
            <li>• Hover over history items to highlight changes in the document</li>
          </ul>
        </div>
      </div>

      {/* History Panel */}
      <HistoryPanel
        editor={editor}
        isOpen={isHistoryOpen}
        onClose={() => setIsHistoryOpen(false)}
      />
    </div>
  );
};
