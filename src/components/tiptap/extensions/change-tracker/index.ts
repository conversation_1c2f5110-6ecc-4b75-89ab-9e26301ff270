import { ReplaceStep, Step } from "@tiptap/pm/transform";
import {
  TextSelection,
  Plugin,
  Plugin<PERSON>ey,
  Transaction,
} from "@tiptap/pm/state";
import { Slice, Fragment } from "@tiptap/pm/model";
import {
  Extension,
  Mark,
  getMarkRange,
  getMarksBetween,
  isMarkActive,
  mergeAttributes,
} from "@tiptap/core";
import type { CommandProps, Editor, MarkRange } from "@tiptap/core";

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    trackchange: {};
  }
}

// insert mark
export const InsertionMark = Mark.create({
  name: MARK_INSERTION,
  addAttributes() {
    return {
      "data-op-user-id": {
        type: "string",
        default: () => "",
      },
      "data-op-user-nickname": {
        type: "string",
        default: () => "",
      },
      "data-op-date": {
        type: "string",
        default: () => "",
      },
    };
  },
  parseHTML() {
    return [{ tag: "insert" }];
  },
  renderHTML({ HTMLAttributes }) {
    return [
      "insert",
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes),
      0,
    ];
  },
});

// delete mark
export const DeletionMark = Mark.create({
  name: MARK_DELETION,
  addAttributes() {
    return {
      "data-op-user-id": {
        type: "string",
        default: () => "",
      },
      "data-op-user-nickname": {
        type: "string",
        default: () => "",
      },
      "data-op-date": {
        type: "string",
        default: () => "",
      },
    };
  },
  parseHTML() {
    return [{ tag: "delete" }];
  },
  renderHTML({ HTMLAttributes }) {
    return [
      "delete",
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes),
      0,
    ];
  },
});

export const TrackChangeExtension = Extension.create<{
  enabled: boolean;
  dataOpUserId?: string;
  dataOpUserNickname?: string;
}>({
  name: EXTENSION_NAME,
  addExtensions() {
    return [InsertionMark, DeletionMark];
  },
  addCommands: () => {
    return {};
  },
  onSelectionUpdate(p) {},
  addProseMirrorPlugins(props: { editor: Editor }) {},
  onTransaction: (props: { editor: Editor; transaction: Transaction }) => {},
});

export default TrackChangeExtension;
